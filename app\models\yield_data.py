"""
Yield data models for semiconductor test results
"""

from sqlalchemy import <PERSON>umn, Integer, String, Float, DateTime, Boolean, Text, ForeignKey, JSON, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum

from app.database import Base


class TestStatus(str, enum.Enum):
    """Test status enumeration"""
    PASS = "pass"
    FAIL = "fail"
    ABORT = "abort"
    RETEST = "retest"


class DataSource(str, enum.Enum):
    """Data source enumeration"""
    STDF = "stdf"
    CSV = "csv"
    DATABASE = "database"
    MANUAL = "manual"


class YieldData(Base):
    """Main yield data table for lot-level information"""

    __tablename__ = "yield_data"

    id = Column(Integer, primary_key=True, index=True)

    # Lot identification
    lot_id = Column(String(50), index=True, nullable=False)
    wafer_id = Column(String(50), index=True, nullable=True)
    sublot_id = Column(String(50), nullable=True)

    # Product information
    product_name = Column(String(100), nullable=False)
    product_version = Column(String(20), nullable=True)
    package_type = Column(String(50), nullable=True)

    # Test information
    test_program = Column(String(100), nullable=False)
    test_version = Column(String(20), nullable=True)
    tester_name = Column(String(50), nullable=True)
    test_temperature = Column(Float, nullable=True)

    # Site and facility
    site_id = Column(Integer, ForeignKey("sites.id"), nullable=False)
    facility = Column(String(50), nullable=True)
    test_floor = Column(String(20), nullable=True)

    # Timing
    test_start_time = Column(DateTime(timezone=True), nullable=False)
    test_end_time = Column(DateTime(timezone=True), nullable=True)

    # Yield metrics
    total_die_count = Column(Integer, nullable=False, default=0)
    good_die_count = Column(Integer, nullable=False, default=0)
    yield_percent = Column(Float, nullable=False, default=0.0)

    # Bin information
    bin_summary = Column(JSON, nullable=True)  # Store bin counts as JSON

    # Data source and processing
    data_source = Column(Enum(DataSource), nullable=False)
    source_file_path = Column(String(500), nullable=True)
    processed_at = Column(DateTime(timezone=True), server_default=func.now())
    processed_by = Column(Integer, ForeignKey("users.id"), nullable=True)

    # Quality flags
    is_valid = Column(Boolean, default=True, nullable=False)
    has_anomalies = Column(Boolean, default=False, nullable=False)
    quality_score = Column(Float, nullable=True)  # 0-100 quality score

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    site = relationship("Site", back_populates="yield_data")
    test_results = relationship("TestResult", back_populates="yield_data", cascade="all, delete-orphan")
    wafer_data = relationship("WaferData", back_populates="yield_data", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<YieldData(lot_id='{self.lot_id}', product='{self.product_name}', yield={self.yield_percent:.2f}%)>"

    @property
    def yield_percentage(self) -> float:
        """Calculate yield percentage"""
        if self.total_die_count > 0:
            return (self.good_die_count / self.total_die_count) * 100
        return 0.0

    @property
    def test_duration_minutes(self) -> float:
        """Calculate test duration in minutes"""
        if self.test_end_time and self.test_start_time:
            delta = self.test_end_time - self.test_start_time
            return delta.total_seconds() / 60
        return 0.0


class TestResult(Base):
    """Individual test result data"""

    __tablename__ = "test_results"

    id = Column(Integer, primary_key=True, index=True)
    yield_data_id = Column(Integer, ForeignKey("yield_data.id"), nullable=False)

    # Test identification
    test_name = Column(String(100), nullable=False, index=True)
    test_number = Column(Integer, nullable=True)
    test_type = Column(String(50), nullable=True)  # parametric, functional, etc.

    # Die/Unit identification
    die_x = Column(Integer, nullable=True)
    die_y = Column(Integer, nullable=True)
    unit_number = Column(Integer, nullable=True)

    # Test results
    test_value = Column(Float, nullable=True)
    test_status = Column(Enum(TestStatus), nullable=False)
    bin_number = Column(Integer, nullable=True)

    # Test conditions
    test_temperature = Column(Float, nullable=True)
    test_voltage = Column(Float, nullable=True)
    test_frequency = Column(Float, nullable=True)

    # Limits
    low_limit = Column(Float, nullable=True)
    high_limit = Column(Float, nullable=True)

    # Additional data
    test_time = Column(Float, nullable=True)  # Test execution time in seconds
    retest_count = Column(Integer, default=0)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    yield_data = relationship("YieldData", back_populates="test_results")

    def __repr__(self):
        return f"<TestResult(test_name='{self.test_name}', status='{self.test_status}', value={self.test_value})>"

    @property
    def is_within_limits(self) -> bool:
        """Check if test value is within limits"""
        if self.test_value is None:
            return False

        within_low = self.low_limit is None or self.test_value >= self.low_limit
        within_high = self.high_limit is None or self.test_value <= self.high_limit

        return within_low and within_high


class WaferData(Base):
    """Wafer-level data and mapping information"""

    __tablename__ = "wafer_data"

    id = Column(Integer, primary_key=True, index=True)
    yield_data_id = Column(Integer, ForeignKey("yield_data.id"), nullable=False)

    # Wafer identification
    wafer_id = Column(String(50), nullable=False, index=True)
    wafer_position = Column(Integer, nullable=True)  # Position in cassette

    # Physical properties
    wafer_diameter = Column(Float, nullable=True)  # in mm
    wafer_thickness = Column(Float, nullable=True)  # in um
    die_size_x = Column(Float, nullable=True)  # in mm
    die_size_y = Column(Float, nullable=True)  # in mm

    # Die layout
    total_die_x = Column(Integer, nullable=True)
    total_die_y = Column(Integer, nullable=True)
    total_die_count = Column(Integer, nullable=False, default=0)
    good_die_count = Column(Integer, nullable=False, default=0)

    # Yield metrics
    wafer_yield = Column(Float, nullable=False, default=0.0)

    # Wafer map data (stored as JSON)
    die_map = Column(JSON, nullable=True)  # 2D array of die status
    bin_map = Column(JSON, nullable=True)  # 2D array of bin numbers

    # Process information
    process_step = Column(String(100), nullable=True)
    process_recipe = Column(String(100), nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    yield_data = relationship("YieldData", back_populates="wafer_data")

    def __repr__(self):
        return f"<WaferData(wafer_id='{self.wafer_id}', yield={self.wafer_yield:.2f}%, die_count={self.total_die_count})>"

    @property
    def yield_percentage(self) -> float:
        """Calculate wafer yield percentage"""
        if self.total_die_count > 0:
            return (self.good_die_count / self.total_die_count) * 100
        return 0.0

    @property
    def die_density(self) -> float:
        """Calculate die density (die per cm²)"""
        if self.wafer_diameter and self.die_size_x and self.die_size_y:
            wafer_area = 3.14159 * (self.wafer_diameter / 20) ** 2  # cm²
            die_area = (self.die_size_x / 10) * (self.die_size_y / 10)  # cm²
            return wafer_area / die_area if die_area > 0 else 0
        return 0.0
