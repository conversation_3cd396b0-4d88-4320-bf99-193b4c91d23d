#!/usr/bin/env python3
"""
Database initialization script for YieldDoc
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.database import init_db, SessionLocal
from app.models.user import User, UserRole
from app.models.site import Site
from app.core.security import get_password_hash
import structlog

logger = structlog.get_logger()


def create_default_admin():
    """Create default admin user"""
    db = SessionLocal()
    try:
        # Check if admin user already exists
        admin_user = db.query(User).filter(User.username == "admin").first()
        if admin_user:
            logger.info("Admin user already exists")
            return

        # Create admin user
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            full_name="System Administrator",
            hashed_password=get_password_hash("admin123"),
            role=UserRole.ADMIN,
            is_active=True,
            is_verified=True,
            employee_id="ADMIN001",
            department="IT"
        )

        db.add(admin_user)
        db.commit()
        logger.info("Default admin user created", username="admin", password="admin123")

    except Exception as e:
        logger.error("Failed to create admin user", error=str(e))
        db.rollback()
    finally:
        db.close()


def create_sample_sites():
    """Create sample manufacturing sites"""
    db = SessionLocal()
    try:
        # Check if sites already exist
        if db.query(Site).count() > 0:
            logger.info("Sites already exist")
            return

        sites = [
            Site(
                site_code="FAB1",
                site_name="Fab 1 - Main Production",
                location="San Jose, CA",
                country="USA",
                contact_email="<EMAIL>",
                manager_name="John Smith",
                timezone="America/Los_Angeles",
                max_wafers_per_day=1000,
                number_of_lines=4
            ),
            Site(
                site_code="FAB2",
                site_name="Fab 2 - Advanced Node",
                location="Austin, TX",
                country="USA",
                contact_email="<EMAIL>",
                manager_name="Jane Doe",
                timezone="America/Chicago",
                max_wafers_per_day=800,
                number_of_lines=3
            ),
            Site(
                site_code="ASM1",
                site_name="Assembly Site 1",
                location="Penang, Malaysia",
                country="Malaysia",
                contact_email="<EMAIL>",
                manager_name="Ahmad Rahman",
                timezone="Asia/Kuala_Lumpur",
                max_wafers_per_day=2000,
                number_of_lines=6
            )
        ]

        for site in sites:
            db.add(site)

        db.commit()
        logger.info("Sample sites created", count=len(sites))

    except Exception as e:
        logger.error("Failed to create sample sites", error=str(e))
        db.rollback()
    finally:
        db.close()


def main():
    """Main initialization function"""
    logger.info("Starting database initialization...")

    try:
        # Initialize database tables
        init_db()
        logger.info("Database tables created")

        # Create default admin user
        create_default_admin()

        # Create sample sites
        create_sample_sites()

        logger.info("Database initialization completed successfully")

    except Exception as e:
        logger.error("Database initialization failed", error=str(e))
        sys.exit(1)


if __name__ == "__main__":
    main()
