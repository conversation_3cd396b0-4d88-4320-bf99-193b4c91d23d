"""
Dashboard API endpoints for KPI data and chart information
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_, or_
import structlog

from app.database import get_db
from app.models.user import User
from app.models.yield_data import YieldData, TestResult, WaferData
from app.models.site import Site
from app.models.report import Report, ReportStatus
from app.core.auth import get_current_verified_user
from app.services.statistical_analyzer import StatisticalAnalyzer

router = APIRouter()
logger = structlog.get_logger()


@router.get("/kpis")
async def get_dashboard_kpis(
    site_id: Optional[int] = Query(None),
    days: int = Query(30, ge=1, le=365),
    current_user: User = Depends(get_current_verified_user),
    db: Session = Depends(get_db)
):
    """
    Get key performance indicators for dashboard
    """
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days)

    # Base query
    query = db.query(YieldData).filter(
        YieldData.test_start_time >= start_date,
        YieldData.test_start_time <= end_date
    )

    if site_id:
        query = query.filter(YieldData.site_id == site_id)

    # Calculate KPIs
    total_lots = query.count()

    if total_lots == 0:
        return {
            "total_lots": 0,
            "average_yield": 0.0,
            "total_wafers": 0,
            "total_die_count": 0,
            "good_die_count": 0,
            "overall_yield": 0.0,
            "yield_trend": "stable",
            "top_products": [],
            "site_performance": [],
            "recent_anomalies": 0
        }

    # Aggregate statistics
    stats = query.with_entities(
        func.avg(YieldData.yield_percent).label('avg_yield'),
        func.sum(YieldData.total_die_count).label('total_die'),
        func.sum(YieldData.good_die_count).label('good_die'),
        func.count(func.distinct(YieldData.wafer_id)).label('total_wafers')
    ).first()

    # Calculate overall yield
    overall_yield = 0.0
    if stats.total_die and stats.good_die:
        overall_yield = (stats.good_die / stats.total_die) * 100

    # Yield trend calculation (compare with previous period)
    prev_start = start_date - timedelta(days=days)
    prev_query = db.query(YieldData).filter(
        YieldData.test_start_time >= prev_start,
        YieldData.test_start_time < start_date
    )

    if site_id:
        prev_query = prev_query.filter(YieldData.site_id == site_id)

    prev_avg = prev_query.with_entities(func.avg(YieldData.yield_percent)).scalar() or 0
    current_avg = stats.avg_yield or 0

    if prev_avg == 0:
        yield_trend = "stable"
    elif current_avg > prev_avg * 1.02:  # 2% improvement
        yield_trend = "improving"
    elif current_avg < prev_avg * 0.98:  # 2% decline
        yield_trend = "declining"
    else:
        yield_trend = "stable"

    # Top products by volume
    top_products = query.with_entities(
        YieldData.product_name,
        func.count(YieldData.id).label('lot_count'),
        func.avg(YieldData.yield_percent).label('avg_yield')
    ).group_by(YieldData.product_name).order_by(desc('lot_count')).limit(5).all()

    # Site performance (if not filtered by site)
    site_performance = []
    if not site_id:
        site_perf = db.query(
            Site.site_code,
            Site.site_name,
            func.count(YieldData.id).label('lot_count'),
            func.avg(YieldData.yield_percent).label('avg_yield')
        ).join(YieldData).filter(
            YieldData.test_start_time >= start_date,
            YieldData.test_start_time <= end_date
        ).group_by(Site.id, Site.site_code, Site.site_name).all()

        site_performance = [
            {
                "site_code": site.site_code,
                "site_name": site.site_name,
                "lot_count": site.lot_count,
                "average_yield": round(site.avg_yield, 2)
            }
            for site in site_perf
        ]

    # Recent anomalies count
    recent_anomalies = query.filter(YieldData.has_anomalies == True).count()

    result = {
        "total_lots": total_lots,
        "average_yield": round(current_avg, 2),
        "total_wafers": stats.total_wafers or 0,
        "total_die_count": stats.total_die or 0,
        "good_die_count": stats.good_die or 0,
        "overall_yield": round(overall_yield, 2),
        "yield_trend": yield_trend,
        "top_products": [
            {
                "product_name": prod.product_name,
                "lot_count": prod.lot_count,
                "average_yield": round(prod.avg_yield, 2)
            }
            for prod in top_products
        ],
        "site_performance": site_performance,
        "recent_anomalies": recent_anomalies
    }

    logger.info(
        "Retrieved dashboard KPIs",
        user_id=current_user.id,
        site_id=site_id,
        days=days,
        total_lots=total_lots,
        average_yield=result["average_yield"]
    )

    return result


@router.get("/yield-trend")
async def get_yield_trend(
    site_id: Optional[int] = Query(None),
    product_name: Optional[str] = Query(None),
    days: int = Query(30, ge=1, le=365),
    granularity: str = Query("daily", regex="^(daily|weekly|monthly)$"),
    current_user: User = Depends(get_current_verified_user),
    db: Session = Depends(get_db)
):
    """
    Get yield trend data for charts
    """
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days)

    # Base query
    query = db.query(YieldData).filter(
        YieldData.test_start_time >= start_date,
        YieldData.test_start_time <= end_date
    )

    if site_id:
        query = query.filter(YieldData.site_id == site_id)

    if product_name:
        query = query.filter(YieldData.product_name.ilike(f"%{product_name}%"))

    # Group by time period based on granularity
    if granularity == "daily":
        time_group = func.date(YieldData.test_start_time)
    elif granularity == "weekly":
        time_group = func.date_trunc('week', YieldData.test_start_time)
    else:  # monthly
        time_group = func.date_trunc('month', YieldData.test_start_time)

    trend_data = query.with_entities(
        time_group.label('period'),
        func.avg(YieldData.yield_percent).label('avg_yield'),
        func.count(YieldData.id).label('lot_count'),
        func.sum(YieldData.total_die_count).label('total_die'),
        func.sum(YieldData.good_die_count).label('good_die')
    ).group_by(time_group).order_by(time_group).all()

    result = []
    for period in trend_data:
        overall_yield = 0.0
        if period.total_die and period.good_die:
            overall_yield = (period.good_die / period.total_die) * 100

        result.append({
            "period": str(period.period),
            "average_yield": round(period.avg_yield, 2),
            "overall_yield": round(overall_yield, 2),
            "lot_count": period.lot_count,
            "total_die_count": period.total_die,
            "good_die_count": period.good_die
        })

    logger.info(
        "Retrieved yield trend data",
        user_id=current_user.id,
        site_id=site_id,
        product_name=product_name,
        days=days,
        granularity=granularity,
        data_points=len(result)
    )

    return result


@router.get("/wafer-map/{yield_data_id}")
async def get_wafer_map(
    yield_data_id: int,
    current_user: User = Depends(get_current_verified_user),
    db: Session = Depends(get_db)
):
    """
    Get wafer map data for visualization
    """
    # Get yield data
    yield_data = db.query(YieldData).filter(YieldData.id == yield_data_id).first()
    if not yield_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Yield data not found"
        )

    # Get wafer data
    wafer_data = db.query(WaferData).filter(WaferData.yield_data_id == yield_data_id).all()

    result = {
        "yield_data_id": yield_data_id,
        "lot_id": yield_data.lot_id,
        "product_name": yield_data.product_name,
        "overall_yield": yield_data.yield_percent,
        "wafers": []
    }

    for wafer in wafer_data:
        wafer_info = {
            "wafer_id": wafer.wafer_id,
            "wafer_yield": wafer.wafer_yield,
            "total_die_count": wafer.total_die_count,
            "good_die_count": wafer.good_die_count,
            "die_map": wafer.die_map,
            "bin_map": wafer.bin_map,
            "die_size_x": wafer.die_size_x,
            "die_size_y": wafer.die_size_y,
            "total_die_x": wafer.total_die_x,
            "total_die_y": wafer.total_die_y
        }
        result["wafers"].append(wafer_info)

    logger.info(
        "Retrieved wafer map data",
        user_id=current_user.id,
        yield_data_id=yield_data_id,
        wafer_count=len(wafer_data)
    )

    return result


@router.get("/pareto-analysis")
async def get_pareto_analysis(
    site_id: Optional[int] = Query(None),
    product_name: Optional[str] = Query(None),
    days: int = Query(30, ge=1, le=365),
    current_user: User = Depends(get_current_verified_user),
    db: Session = Depends(get_db)
):
    """
    Get Pareto analysis of failure bins
    """
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days)

    # Base query for test results
    query = db.query(TestResult).join(YieldData).filter(
        YieldData.test_start_time >= start_date,
        YieldData.test_start_time <= end_date,
        TestResult.test_status == "fail"
    )

    if site_id:
        query = query.filter(YieldData.site_id == site_id)

    if product_name:
        query = query.filter(YieldData.product_name.ilike(f"%{product_name}%"))

    # Group by bin number and count failures
    bin_analysis = query.with_entities(
        TestResult.bin_number,
        func.count(TestResult.id).label('failure_count')
    ).filter(TestResult.bin_number.isnot(None)).group_by(
        TestResult.bin_number
    ).order_by(desc('failure_count')).all()

    # Calculate cumulative percentages
    total_failures = sum(bin_data.failure_count for bin_data in bin_analysis)

    result = []
    cumulative_count = 0

    for bin_data in bin_analysis:
        cumulative_count += bin_data.failure_count
        percentage = (bin_data.failure_count / total_failures) * 100 if total_failures > 0 else 0
        cumulative_percentage = (cumulative_count / total_failures) * 100 if total_failures > 0 else 0

        result.append({
            "bin_number": bin_data.bin_number,
            "failure_count": bin_data.failure_count,
            "percentage": round(percentage, 2),
            "cumulative_percentage": round(cumulative_percentage, 2)
        })

    logger.info(
        "Retrieved Pareto analysis",
        user_id=current_user.id,
        site_id=site_id,
        product_name=product_name,
        total_failures=total_failures,
        bin_count=len(result)
    )

    return {
        "total_failures": total_failures,
        "bins": result
    }


@router.get("/control-limits")
async def get_control_limits(
    test_name: str = Query(...),
    site_id: Optional[int] = Query(None),
    product_name: Optional[str] = Query(None),
    days: int = Query(90, ge=1, le=365),
    current_user: User = Depends(get_current_verified_user),
    db: Session = Depends(get_db)
):
    """
    Get control limits for a specific test
    """
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days)

    # Query test results
    query = db.query(TestResult).join(YieldData).filter(
        YieldData.test_start_time >= start_date,
        YieldData.test_start_time <= end_date,
        TestResult.test_name == test_name,
        TestResult.test_value.isnot(None)
    )

    if site_id:
        query = query.filter(YieldData.site_id == site_id)

    if product_name:
        query = query.filter(YieldData.product_name.ilike(f"%{product_name}%"))

    # Get test values
    test_results = query.with_entities(
        TestResult.test_value,
        YieldData.test_start_time
    ).order_by(YieldData.test_start_time).all()

    if not test_results:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No test data found for the specified criteria"
        )

    # Use StatisticalAnalyzer to calculate control limits
    analyzer = StatisticalAnalyzer()
    values = [result.test_value for result in test_results]
    control_limits = analyzer.calculate_control_limits(values)

    # Prepare time series data
    time_series = [
        {
            "timestamp": result.test_start_time.isoformat(),
            "value": result.test_value
        }
        for result in test_results
    ]

    result = {
        "test_name": test_name,
        "data_points": len(test_results),
        "control_limits": control_limits,
        "time_series": time_series
    }

    logger.info(
        "Retrieved control limits",
        user_id=current_user.id,
        test_name=test_name,
        site_id=site_id,
        product_name=product_name,
        data_points=len(test_results)
    )

    return result


@router.get("/anomalies")
async def get_anomalies(
    site_id: Optional[int] = Query(None),
    days: int = Query(7, ge=1, le=30),
    current_user: User = Depends(get_current_verified_user),
    db: Session = Depends(get_db)
):
    """
    Get recent anomalies and outliers
    """
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days)

    # Query yield data with anomalies
    query = db.query(YieldData).filter(
        YieldData.test_start_time >= start_date,
        YieldData.test_start_time <= end_date,
        YieldData.has_anomalies == True
    )

    if site_id:
        query = query.filter(YieldData.site_id == site_id)

    anomalies = query.order_by(desc(YieldData.test_start_time)).all()

    result = []
    for anomaly in anomalies:
        result.append({
            "id": anomaly.id,
            "lot_id": anomaly.lot_id,
            "product_name": anomaly.product_name,
            "site_id": anomaly.site_id,
            "yield_percent": anomaly.yield_percent,
            "test_start_time": anomaly.test_start_time.isoformat(),
            "quality_score": anomaly.quality_score,
            "total_die_count": anomaly.total_die_count,
            "good_die_count": anomaly.good_die_count
        })

    logger.info(
        "Retrieved anomalies",
        user_id=current_user.id,
        site_id=site_id,
        days=days,
        anomaly_count=len(result)
    )

    return {
        "anomalies": result,
        "count": len(result)
    }
