"""
Yield data schemas for API request/response models
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, validator

from app.models.yield_data import DataSource, TestStatus


class YieldDataBase(BaseModel):
    """Base yield data schema"""
    lot_id: str
    wafer_id: Optional[str] = None
    sublot_id: Optional[str] = None
    product_name: str
    product_version: Optional[str] = None
    package_type: Optional[str] = None
    test_program: str
    test_version: Optional[str] = None
    tester_name: Optional[str] = None
    test_temperature: Optional[float] = None
    site_id: int
    facility: Optional[str] = None
    test_floor: Optional[str] = None
    test_start_time: datetime
    test_end_time: Optional[datetime] = None
    total_die_count: int = 0
    good_die_count: int = 0
    bin_summary: Optional[Dict[str, Any]] = None


class YieldDataCreate(YieldDataBase):
    """Yield data creation schema"""

    @validator('good_die_count')
    def validate_good_die_count(cls, v, values):
        if 'total_die_count' in values and v > values['total_die_count']:
            raise ValueError('Good die count cannot exceed total die count')
        return v

    @validator('test_end_time')
    def validate_test_end_time(cls, v, values):
        if v and 'test_start_time' in values and v < values['test_start_time']:
            raise ValueError('Test end time must be after start time')
        return v


class YieldDataUpdate(BaseModel):
    """Yield data update schema"""
    wafer_id: Optional[str] = None
    sublot_id: Optional[str] = None
    product_version: Optional[str] = None
    package_type: Optional[str] = None
    test_version: Optional[str] = None
    tester_name: Optional[str] = None
    test_temperature: Optional[float] = None
    facility: Optional[str] = None
    test_floor: Optional[str] = None
    test_end_time: Optional[datetime] = None
    total_die_count: Optional[int] = None
    good_die_count: Optional[int] = None
    bin_summary: Optional[Dict[str, Any]] = None
    is_valid: Optional[bool] = None
    has_anomalies: Optional[bool] = None
    quality_score: Optional[float] = None


class YieldData(YieldDataBase):
    """Yield data response schema"""
    id: int
    yield_percent: float
    data_source: DataSource
    source_file_path: Optional[str] = None
    processed_at: datetime
    processed_by: Optional[int] = None
    is_valid: bool
    has_anomalies: bool
    quality_score: Optional[float] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True


class TestResultBase(BaseModel):
    """Base test result schema"""
    test_name: str
    test_number: Optional[int] = None
    test_type: Optional[str] = None
    die_x: Optional[int] = None
    die_y: Optional[int] = None
    unit_number: Optional[int] = None
    test_value: Optional[float] = None
    test_status: TestStatus
    bin_number: Optional[int] = None
    test_temperature: Optional[float] = None
    test_voltage: Optional[float] = None
    test_frequency: Optional[float] = None
    low_limit: Optional[float] = None
    high_limit: Optional[float] = None
    test_time: Optional[float] = None
    retest_count: int = 0


class TestResultCreate(TestResultBase):
    """Test result creation schema"""
    yield_data_id: int


class TestResult(TestResultBase):
    """Test result response schema"""
    id: int
    yield_data_id: int
    created_at: datetime

    class Config:
        orm_mode = True


class WaferDataBase(BaseModel):
    """Base wafer data schema"""
    wafer_id: str
    wafer_position: Optional[int] = None
    wafer_diameter: Optional[float] = None
    wafer_thickness: Optional[float] = None
    die_size_x: Optional[float] = None
    die_size_y: Optional[float] = None
    total_die_x: Optional[int] = None
    total_die_y: Optional[int] = None
    total_die_count: int = 0
    good_die_count: int = 0
    die_map: Optional[List[List[int]]] = None
    bin_map: Optional[List[List[int]]] = None
    process_step: Optional[str] = None
    process_recipe: Optional[str] = None


class WaferDataCreate(WaferDataBase):
    """Wafer data creation schema"""
    yield_data_id: int

    @validator('good_die_count')
    def validate_good_die_count(cls, v, values):
        if 'total_die_count' in values and v > values['total_die_count']:
            raise ValueError('Good die count cannot exceed total die count')
        return v


class WaferData(WaferDataBase):
    """Wafer data response schema"""
    id: int
    yield_data_id: int
    wafer_yield: float
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True


class YieldDataWithDetails(YieldData):
    """Yield data with related test results and wafer data"""
    test_results: List[TestResult] = []
    wafer_data: List[WaferData] = []

    class Config:
        orm_mode = True
