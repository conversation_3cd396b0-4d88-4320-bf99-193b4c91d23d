"""
Yield Data API endpoints for CRUD operations and file processing
"""

from typing import List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Query
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func
import structlog

from app.database import get_db
from app.models.user import User
from app.models.yield_data import YieldData, TestResult, WaferData, DataSource
from app.models.site import Site
from app.schemas.yield_data import (
    YieldDataCreate,
    YieldDataUpdate,
    YieldData as YieldDataSchema,
    TestResult as TestResultSchema,
    WaferData as WaferDataSchema
)
from app.core.auth import get_current_verified_user, get_current_editor_user
from app.services.data_processor import DataProcessor
from app.config import settings

router = APIRouter()
logger = structlog.get_logger()


@router.get("/", response_model=List[YieldDataSchema])
async def get_yield_data(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    site_id: Optional[int] = Query(None),
    product_name: Optional[str] = Query(None),
    lot_id: Optional[str] = Query(None),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    min_yield: Optional[float] = Query(None, ge=0, le=100),
    max_yield: Optional[float] = Query(None, ge=0, le=100),
    current_user: User = Depends(get_current_verified_user),
    db: Session = Depends(get_db)
):
    """
    Get yield data with filtering and pagination
    """
    query = db.query(YieldData)

    # Apply filters
    if site_id:
        query = query.filter(YieldData.site_id == site_id)

    if product_name:
        query = query.filter(YieldData.product_name.ilike(f"%{product_name}%"))

    if lot_id:
        query = query.filter(YieldData.lot_id.ilike(f"%{lot_id}%"))

    if start_date:
        query = query.filter(YieldData.test_start_time >= start_date)

    if end_date:
        query = query.filter(YieldData.test_start_time <= end_date)

    if min_yield is not None:
        query = query.filter(YieldData.yield_percent >= min_yield)

    if max_yield is not None:
        query = query.filter(YieldData.yield_percent <= max_yield)

    # Order by test start time (newest first)
    query = query.order_by(desc(YieldData.test_start_time))

    # Apply pagination
    yield_data = query.offset(skip).limit(limit).all()

    logger.info(
        "Retrieved yield data",
        user_id=current_user.id,
        count=len(yield_data),
        filters={
            "site_id": site_id,
            "product_name": product_name,
            "lot_id": lot_id,
            "start_date": start_date,
            "end_date": end_date
        }
    )

    return yield_data


@router.get("/{yield_data_id}", response_model=YieldDataSchema)
async def get_yield_data_by_id(
    yield_data_id: int,
    current_user: User = Depends(get_current_verified_user),
    db: Session = Depends(get_db)
):
    """
    Get specific yield data by ID
    """
    yield_data = db.query(YieldData).filter(YieldData.id == yield_data_id).first()

    if not yield_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Yield data not found"
        )

    logger.info(
        "Retrieved yield data by ID",
        user_id=current_user.id,
        yield_data_id=yield_data_id,
        lot_id=yield_data.lot_id
    )

    return yield_data


@router.post("/", response_model=YieldDataSchema)
async def create_yield_data(
    yield_data: YieldDataCreate,
    current_user: User = Depends(get_current_editor_user),
    db: Session = Depends(get_db)
):
    """
    Create new yield data entry
    """
    # Verify site exists
    site = db.query(Site).filter(Site.id == yield_data.site_id).first()
    if not site:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Site not found"
        )

    # Check for duplicate lot_id at the same site
    existing = db.query(YieldData).filter(
        and_(
            YieldData.lot_id == yield_data.lot_id,
            YieldData.site_id == yield_data.site_id,
            YieldData.test_program == yield_data.test_program
        )
    ).first()

    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Yield data for this lot already exists"
        )

    # Create yield data
    db_yield_data = YieldData(
        **yield_data.dict(),
        processed_by=current_user.id,
        data_source=DataSource.MANUAL
    )

    # Calculate yield percentage
    if db_yield_data.total_die_count > 0:
        db_yield_data.yield_percent = (db_yield_data.good_die_count / db_yield_data.total_die_count) * 100

    db.add(db_yield_data)
    db.commit()
    db.refresh(db_yield_data)

    logger.info(
        "Created yield data",
        user_id=current_user.id,
        yield_data_id=db_yield_data.id,
        lot_id=db_yield_data.lot_id,
        yield_percent=db_yield_data.yield_percent
    )

    return db_yield_data


@router.put("/{yield_data_id}", response_model=YieldDataSchema)
async def update_yield_data(
    yield_data_id: int,
    yield_data_update: YieldDataUpdate,
    current_user: User = Depends(get_current_editor_user),
    db: Session = Depends(get_db)
):
    """
    Update existing yield data
    """
    db_yield_data = db.query(YieldData).filter(YieldData.id == yield_data_id).first()

    if not db_yield_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Yield data not found"
        )

    # Update fields
    update_data = yield_data_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_yield_data, field, value)

    # Recalculate yield percentage if die counts changed
    if 'total_die_count' in update_data or 'good_die_count' in update_data:
        if db_yield_data.total_die_count > 0:
            db_yield_data.yield_percent = (db_yield_data.good_die_count / db_yield_data.total_die_count) * 100

    db.commit()
    db.refresh(db_yield_data)

    logger.info(
        "Updated yield data",
        user_id=current_user.id,
        yield_data_id=yield_data_id,
        updated_fields=list(update_data.keys())
    )

    return db_yield_data


@router.delete("/{yield_data_id}")
async def delete_yield_data(
    yield_data_id: int,
    current_user: User = Depends(get_current_editor_user),
    db: Session = Depends(get_db)
):
    """
    Delete yield data
    """
    db_yield_data = db.query(YieldData).filter(YieldData.id == yield_data_id).first()

    if not db_yield_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Yield data not found"
        )

    db.delete(db_yield_data)
    db.commit()

    logger.info(
        "Deleted yield data",
        user_id=current_user.id,
        yield_data_id=yield_data_id,
        lot_id=db_yield_data.lot_id
    )

    return {"message": "Yield data deleted successfully"}


@router.post("/upload/stdf")
async def upload_stdf_file(
    file: UploadFile = File(...),
    site_id: int = Query(...),
    current_user: User = Depends(get_current_editor_user),
    db: Session = Depends(get_db)
):
    """
    Upload and process STDF file
    """
    # Validate file type
    if not file.filename.lower().endswith(('.stdf', '.std')):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid file type. Only STDF files are allowed."
        )

    # Validate file size
    if file.size > settings.MAX_UPLOAD_SIZE:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"File too large. Maximum size is {settings.MAX_UPLOAD_SIZE} bytes."
        )

    # Verify site exists
    site = db.query(Site).filter(Site.id == site_id).first()
    if not site:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Site not found"
        )

    try:
        # Process STDF file using DataProcessor service
        processor = DataProcessor(db)
        result = await processor.process_stdf_file(
            file=file,
            site_id=site_id,
            user_id=current_user.id
        )

        logger.info(
            "STDF file uploaded and processed",
            user_id=current_user.id,
            filename=file.filename,
            site_id=site_id,
            lots_processed=result.get('lots_processed', 0)
        )

        return {
            "message": "STDF file processed successfully",
            "filename": file.filename,
            "lots_processed": result.get('lots_processed', 0),
            "yield_data_ids": result.get('yield_data_ids', [])
        }

    except Exception as e:
        logger.error(
            "STDF file processing failed",
            user_id=current_user.id,
            filename=file.filename,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process STDF file: {str(e)}"
        )


@router.post("/upload/csv")
async def upload_csv_file(
    file: UploadFile = File(...),
    site_id: int = Query(...),
    current_user: User = Depends(get_current_editor_user),
    db: Session = Depends(get_db)
):
    """
    Upload and process CSV file
    """
    # Validate file type
    if not file.filename.lower().endswith('.csv'):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid file type. Only CSV files are allowed."
        )

    # Validate file size
    if file.size > settings.MAX_UPLOAD_SIZE:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"File too large. Maximum size is {settings.MAX_UPLOAD_SIZE} bytes."
        )

    # Verify site exists
    site = db.query(Site).filter(Site.id == site_id).first()
    if not site:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Site not found"
        )

    try:
        # Process CSV file using DataProcessor service
        processor = DataProcessor(db)
        result = await processor.process_csv_file(
            file=file,
            site_id=site_id,
            user_id=current_user.id
        )

        logger.info(
            "CSV file uploaded and processed",
            user_id=current_user.id,
            filename=file.filename,
            site_id=site_id,
            records_processed=result.get('records_processed', 0)
        )

        return {
            "message": "CSV file processed successfully",
            "filename": file.filename,
            "records_processed": result.get('records_processed', 0),
            "yield_data_ids": result.get('yield_data_ids', [])
        }

    except Exception as e:
        logger.error(
            "CSV file processing failed",
            user_id=current_user.id,
            filename=file.filename,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process CSV file: {str(e)}"
        )


@router.get("/statistics")
async def get_yield_statistics(
    site_id: Optional[int] = Query(None),
    product_name: Optional[str] = Query(None),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    current_user: User = Depends(get_current_verified_user),
    db: Session = Depends(get_db)
):
    """
    Get yield statistics and KPIs
    """
    query = db.query(YieldData)

    # Apply filters
    if site_id:
        query = query.filter(YieldData.site_id == site_id)

    if product_name:
        query = query.filter(YieldData.product_name.ilike(f"%{product_name}%"))

    if start_date:
        query = query.filter(YieldData.test_start_time >= start_date)

    if end_date:
        query = query.filter(YieldData.test_start_time <= end_date)

    # Calculate statistics
    stats = query.with_entities(
        func.count(YieldData.id).label('total_lots'),
        func.avg(YieldData.yield_percent).label('average_yield'),
        func.min(YieldData.yield_percent).label('min_yield'),
        func.max(YieldData.yield_percent).label('max_yield'),
        func.sum(YieldData.total_die_count).label('total_die_count'),
        func.sum(YieldData.good_die_count).label('total_good_die_count')
    ).first()

    # Get yield trend data (last 30 days)
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    trend_query = query.filter(YieldData.test_start_time >= thirty_days_ago)

    daily_yields = trend_query.with_entities(
        func.date(YieldData.test_start_time).label('date'),
        func.avg(YieldData.yield_percent).label('avg_yield'),
        func.count(YieldData.id).label('lot_count')
    ).group_by(func.date(YieldData.test_start_time)).order_by(func.date(YieldData.test_start_time)).all()

    # Calculate overall yield percentage
    overall_yield = 0.0
    if stats.total_die_count and stats.total_good_die_count:
        overall_yield = (stats.total_good_die_count / stats.total_die_count) * 100

    result = {
        "summary": {
            "total_lots": stats.total_lots or 0,
            "average_yield": round(stats.average_yield or 0, 2),
            "overall_yield": round(overall_yield, 2),
            "min_yield": round(stats.min_yield or 0, 2),
            "max_yield": round(stats.max_yield or 0, 2),
            "total_die_count": stats.total_die_count or 0,
            "total_good_die_count": stats.total_good_die_count or 0
        },
        "trend": [
            {
                "date": str(day.date),
                "average_yield": round(day.avg_yield, 2),
                "lot_count": day.lot_count
            }
            for day in daily_yields
        ]
    }

    logger.info(
        "Retrieved yield statistics",
        user_id=current_user.id,
        total_lots=result["summary"]["total_lots"],
        average_yield=result["summary"]["average_yield"]
    )

    return result
