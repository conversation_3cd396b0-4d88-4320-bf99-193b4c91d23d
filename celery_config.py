"""
Celery configuration for YieldDoc
"""

from celery import Celery
from celery.schedules import crontab
from app.config import settings

# Create Celery instance
celery_app = Celery(
    "yielddoc",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
    include=["app.tasks.data_processing", "app.tasks.report_generation", "app.tasks.email_tasks"]
)

# Celery configuration
celery_app.conf.update(
    # Task settings
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,

    # Worker settings
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_max_tasks_per_child=1000,

    # Result backend settings
    result_expires=3600,  # 1 hour
    result_backend_transport_options={
        "master_name": "mymaster",
        "visibility_timeout": 3600,
    },

    # Task routing
    task_routes={
        "app.tasks.data_processing.*": {"queue": "data_processing"},
        "app.tasks.report_generation.*": {"queue": "report_generation"},
        "app.tasks.email_tasks.*": {"queue": "email"},
    },

    # Beat schedule for periodic tasks
    beat_schedule={
        # Daily yield report generation
        "generate-daily-reports": {
            "task": "app.tasks.report_generation.generate_daily_reports",
            "schedule": crontab(hour=6, minute=0),  # 6:00 AM daily
        },

        # Weekly yield report generation
        "generate-weekly-reports": {
            "task": "app.tasks.report_generation.generate_weekly_reports",
            "schedule": crontab(hour=7, minute=0, day_of_week=1),  # 7:00 AM every Monday
        },

        # Monthly yield report generation
        "generate-monthly-reports": {
            "task": "app.tasks.report_generation.generate_monthly_reports",
            "schedule": crontab(hour=8, minute=0, day=1),  # 8:00 AM on 1st of month
        },

        # Clean up old files
        "cleanup-old-files": {
            "task": "app.tasks.data_processing.cleanup_old_files",
            "schedule": crontab(hour=2, minute=0),  # 2:00 AM daily
        },

        # Health check
        "health-check": {
            "task": "app.tasks.data_processing.health_check",
            "schedule": crontab(minute="*/15"),  # Every 15 minutes
        },
    },
)

# Task annotations for specific task configurations
celery_app.conf.task_annotations = {
    "app.tasks.data_processing.process_stdf_file": {
        "rate_limit": "10/m",  # 10 tasks per minute
        "time_limit": 300,     # 5 minutes
        "soft_time_limit": 240, # 4 minutes
    },
    "app.tasks.report_generation.generate_report": {
        "rate_limit": "5/m",   # 5 tasks per minute
        "time_limit": 600,     # 10 minutes
        "soft_time_limit": 540, # 9 minutes
    },
    "app.tasks.email_tasks.send_email": {
        "rate_limit": "30/m",  # 30 emails per minute
        "time_limit": 60,      # 1 minute
        "retry_kwargs": {"max_retries": 3, "countdown": 60},
    },
}

if __name__ == "__main__":
    celery_app.start()
