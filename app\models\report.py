"""
Report models for automated report generation
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, ForeignKey, JSON, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum

from app.database import Base


class ReportType(str, enum.Enum):
    """Report type enumeration"""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    CUSTOM = "custom"


class ReportStatus(str, enum.Enum):
    """Report status enumeration"""
    PENDING = "pending"
    GENERATING = "generating"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ReportFormat(str, enum.Enum):
    """Report format enumeration"""
    PDF = "pdf"
    DOCX = "docx"
    XLSX = "xlsx"
    HTML = "html"
    JSON = "json"


class Report(Base):
    """Generated report model"""

    __tablename__ = "reports"

    id = Column(Integer, primary_key=True, index=True)

    # Report identification
    report_name = Column(String(200), nullable=False)
    report_type = Column(Enum(ReportType), nullable=False)
    report_format = Column(Enum(ReportFormat), nullable=False)

    # Report content
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)

    # Time period
    period_start = Column(DateTime(timezone=True), nullable=False)
    period_end = Column(DateTime(timezone=True), nullable=False)

    # Filters and parameters
    site_ids = Column(JSON, nullable=True)  # List of site IDs
    product_names = Column(JSON, nullable=True)  # List of product names
    filters = Column(JSON, nullable=True)  # Additional filters
    parameters = Column(JSON, nullable=True)  # Report parameters

    # Generation info
    status = Column(Enum(ReportStatus), default=ReportStatus.PENDING, nullable=False)
    generated_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    generated_at = Column(DateTime(timezone=True), nullable=True)

    # File information
    file_path = Column(String(500), nullable=True)
    file_size = Column(Integer, nullable=True)  # Size in bytes
    file_hash = Column(String(64), nullable=True)  # SHA-256 hash

    # Delivery information
    email_recipients = Column(JSON, nullable=True)  # List of email addresses
    is_scheduled = Column(Boolean, default=False, nullable=False)
    schedule_cron = Column(String(100), nullable=True)  # Cron expression
    next_run = Column(DateTime(timezone=True), nullable=True)

    # Statistics
    total_lots = Column(Integer, nullable=True)
    total_wafers = Column(Integer, nullable=True)
    average_yield = Column(Float, nullable=True)

    # Error handling
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0, nullable=False)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    generated_by_user = relationship("User", foreign_keys=[generated_by])
    template = relationship("ReportTemplate", back_populates="reports")
    template_id = Column(Integer, ForeignKey("report_templates.id"), nullable=True)

    def __repr__(self):
        return f"<Report(name='{self.report_name}', type='{self.report_type}', status='{self.status}')>"

    @property
    def is_completed(self) -> bool:
        """Check if report generation is completed"""
        return self.status == ReportStatus.COMPLETED

    @property
    def is_failed(self) -> bool:
        """Check if report generation failed"""
        return self.status == ReportStatus.FAILED

    @property
    def can_retry(self) -> bool:
        """Check if report can be retried"""
        return self.status == ReportStatus.FAILED and self.retry_count < 3


class ReportTemplate(Base):
    """Report template model"""

    __tablename__ = "report_templates"

    id = Column(Integer, primary_key=True, index=True)

    # Template identification
    template_name = Column(String(100), unique=True, nullable=False)
    display_name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)

    # Template configuration
    report_type = Column(Enum(ReportType), nullable=False)
    default_format = Column(Enum(ReportFormat), default=ReportFormat.PDF, nullable=False)

    # Template content
    template_path = Column(String(500), nullable=False)  # Path to template file
    sections = Column(JSON, nullable=True)  # Template sections configuration
    charts = Column(JSON, nullable=True)  # Chart configurations
    tables = Column(JSON, nullable=True)  # Table configurations

    # Default parameters
    default_parameters = Column(JSON, nullable=True)
    required_parameters = Column(JSON, nullable=True)

    # Scheduling
    is_schedulable = Column(Boolean, default=True, nullable=False)
    default_schedule = Column(String(100), nullable=True)  # Default cron expression

    # Access control
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    is_public = Column(Boolean, default=False, nullable=False)

    # Version control
    version = Column(String(20), default="1.0", nullable=False)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    created_by_user = relationship("User", foreign_keys=[created_by])
    reports = relationship("Report", back_populates="template")

    def __repr__(self):
        return f"<ReportTemplate(name='{self.template_name}', type='{self.report_type}', version='{self.version}')>"
