"""
Database configuration and session management
"""

from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
import redis
from typing import Generator

from app.config import settings

# PostgreSQL Database Engine
engine = create_engine(
    settings.DATABASE_URL,
    pool_pre_ping=True,
    pool_recycle=300,
    pool_size=10,
    max_overflow=20,
)

# Session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for models
Base = declarative_base()

# Redis connection
redis_client = redis.Redis.from_url(settings.REDIS_URL, decode_responses=True)


def get_db() -> Generator:
    """
    Database dependency for FastAPI
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_redis() -> redis.Redis:
    """
    Redis dependency for FastAPI
    """
    return redis_client


def init_db() -> None:
    """
    Initialize database tables
    """
    # Import all models here to ensure they are registered with SQLAlchemy
    from app.models import user, yield_data, report, site

    Base.metadata.create_all(bind=engine)


def close_db_connections():
    """
    Close database connections
    """
    engine.dispose()
    redis_client.close()
