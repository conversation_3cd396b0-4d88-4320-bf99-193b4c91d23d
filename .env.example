# Database Configuration
DATABASE_URL=postgresql://yielddoc_user:password@localhost:5432/yielddoc
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=yielddoc
DATABASE_USER=yielddoc_user
DATABASE_PASSWORD=password

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# Application Configuration
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=YieldDoc
PROJECT_VERSION=1.0.0
DEBUG=True

# CORS Configuration
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:8000","http://localhost:8050"]

# Email Configuration
SMTP_TLS=True
SMTP_PORT=587
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=YieldDoc Reports

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/1

# File Upload Configuration
MAX_UPLOAD_SIZE=100000000  # 100MB
UPLOAD_FOLDER=uploads
REPORTS_FOLDER=reports
TEMP_FOLDER=temp

# Dashboard Configuration
DASH_HOST=0.0.0.0
DASH_PORT=8050
DASH_DEBUG=True

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Data Processing Configuration
BATCH_SIZE=1000
MAX_WORKERS=4
PROCESSING_TIMEOUT=3600  # 1 hour

# Report Generation Configuration
REPORT_RETENTION_DAYS=90
AUTO_CLEANUP_ENABLED=True

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_TIMEOUT=3600  # 1 hour

# Monitoring Configuration
ENABLE_METRICS=True
METRICS_PORT=9090

# Company Configuration
COMPANY_NAME=Your Semiconductor Company
COMPANY_LOGO_URL=https://your-company.com/logo.png
TIMEZONE=UTC
