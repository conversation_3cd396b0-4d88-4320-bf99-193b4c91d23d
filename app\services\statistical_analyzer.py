"""
Statistical analysis service for yield calculations and control limits
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timed<PERSON>ta
from sqlalchemy.orm import Session
import structlog

from app.models.yield_data import YieldData, TestResult
from app.models.site import Site

logger = structlog.get_logger()


class StatisticalAnalyzer:
    """Service for statistical analysis of yield data"""

    def __init__(self):
        self.confidence_level = 0.95
        self.control_limit_sigma = 3.0

    def calculate_control_limits(self, values: List[float]) -> Dict[str, float]:
        """
        Calculate statistical control limits for a dataset
        """
        if not values or len(values) < 3:
            return {
                "mean": 0.0,
                "std_dev": 0.0,
                "upper_control_limit": 0.0,
                "lower_control_limit": 0.0,
                "upper_warning_limit": 0.0,
                "lower_warning_limit": 0.0,
                "sample_size": 0
            }

        # Convert to numpy array for calculations
        data = np.array(values)

        # Calculate basic statistics
        mean = np.mean(data)
        std_dev = np.std(data, ddof=1)  # Sample standard deviation

        # Calculate control limits (3-sigma)
        ucl = mean + (self.control_limit_sigma * std_dev)
        lcl = mean - (self.control_limit_sigma * std_dev)

        # Calculate warning limits (2-sigma)
        uwl = mean + (2.0 * std_dev)
        lwl = mean - (2.0 * std_dev)

        return {
            "mean": float(mean),
            "std_dev": float(std_dev),
            "upper_control_limit": float(ucl),
            "lower_control_limit": float(lcl),
            "upper_warning_limit": float(uwl),
            "lower_warning_limit": float(lwl),
            "sample_size": len(values)
        }

    def detect_anomalies(self, values: List[float], timestamps: List[datetime] = None) -> Dict[str, Any]:
        """
        Detect anomalies in yield data using statistical methods
        """
        if not values or len(values) < 5:
            return {
                "anomalies": [],
                "anomaly_count": 0,
                "anomaly_rate": 0.0,
                "method": "insufficient_data"
            }

        data = np.array(values)
        anomalies = []

        # Method 1: Z-score based detection
        z_scores = np.abs((data - np.mean(data)) / np.std(data))
        z_threshold = 2.5
        z_anomalies = np.where(z_scores > z_threshold)[0]

        # Method 2: IQR based detection
        q1 = np.percentile(data, 25)
        q3 = np.percentile(data, 75)
        iqr = q3 - q1
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        iqr_anomalies = np.where((data < lower_bound) | (data > upper_bound))[0]

        # Combine anomalies from both methods
        combined_anomalies = np.unique(np.concatenate([z_anomalies, iqr_anomalies]))

        for idx in combined_anomalies:
            anomaly_info = {
                "index": int(idx),
                "value": float(data[idx]),
                "z_score": float(z_scores[idx]),
                "deviation_from_mean": float(data[idx] - np.mean(data)),
                "severity": "high" if z_scores[idx] > 3.0 else "medium"
            }

            if timestamps and idx < len(timestamps):
                anomaly_info["timestamp"] = timestamps[idx].isoformat()

            anomalies.append(anomaly_info)

        return {
            "anomalies": anomalies,
            "anomaly_count": len(anomalies),
            "anomaly_rate": len(anomalies) / len(values) * 100,
            "method": "z_score_and_iqr",
            "statistics": {
                "mean": float(np.mean(data)),
                "std_dev": float(np.std(data)),
                "q1": float(q1),
                "q3": float(q3),
                "iqr": float(iqr)
            }
        }

    def calculate_yield_trends(self, yield_data: List[Tuple[datetime, float]]) -> Dict[str, Any]:
        """
        Calculate yield trends and patterns
        """
        if not yield_data or len(yield_data) < 3:
            return {
                "trend": "insufficient_data",
                "slope": 0.0,
                "correlation": 0.0,
                "volatility": 0.0
            }

        # Sort by timestamp
        sorted_data = sorted(yield_data, key=lambda x: x[0])
        timestamps = [item[0] for item in sorted_data]
        yields = [item[1] for item in sorted_data]

        # Convert timestamps to numeric values (days since first measurement)
        base_time = timestamps[0]
        time_numeric = [(ts - base_time).total_seconds() / 86400 for ts in timestamps]

        # Calculate linear regression
        correlation_matrix = np.corrcoef(time_numeric, yields)
        correlation = correlation_matrix[0, 1] if len(correlation_matrix) > 1 else 0.0

        # Calculate slope using least squares
        if len(time_numeric) > 1:
            slope = np.polyfit(time_numeric, yields, 1)[0]
        else:
            slope = 0.0

        # Determine trend direction
        if abs(slope) < 0.1:
            trend = "stable"
        elif slope > 0:
            trend = "improving"
        else:
            trend = "declining"

        # Calculate volatility (coefficient of variation)
        mean_yield = np.mean(yields)
        std_yield = np.std(yields)
        volatility = (std_yield / mean_yield * 100) if mean_yield > 0 else 0.0

        return {
            "trend": trend,
            "slope": float(slope),
            "correlation": float(correlation),
            "volatility": float(volatility),
            "mean_yield": float(mean_yield),
            "std_yield": float(std_yield),
            "data_points": len(yield_data)
        }

    def calculate_pareto_analysis(self, failure_data: Dict[str, int]) -> List[Dict[str, Any]]:
        """
        Calculate Pareto analysis for failure modes
        """
        if not failure_data:
            return []

        # Sort by failure count (descending)
        sorted_failures = sorted(failure_data.items(), key=lambda x: x[1], reverse=True)
        total_failures = sum(failure_data.values())

        pareto_data = []
        cumulative_count = 0

        for failure_mode, count in sorted_failures:
            cumulative_count += count
            percentage = (count / total_failures) * 100
            cumulative_percentage = (cumulative_count / total_failures) * 100

            pareto_data.append({
                "failure_mode": failure_mode,
                "count": count,
                "percentage": round(percentage, 2),
                "cumulative_percentage": round(cumulative_percentage, 2)
            })

        return pareto_data

    def calculate_cpk(self, values: List[float], lower_spec: float, upper_spec: float) -> Dict[str, float]:
        """
        Calculate process capability index (Cpk)
        """
        if not values or len(values) < 3:
            return {
                "cpk": 0.0,
                "cp": 0.0,
                "cpu": 0.0,
                "cpl": 0.0,
                "mean": 0.0,
                "std_dev": 0.0
            }

        data = np.array(values)
        mean = np.mean(data)
        std_dev = np.std(data, ddof=1)

        if std_dev == 0:
            return {
                "cpk": float('inf'),
                "cp": float('inf'),
                "cpu": float('inf'),
                "cpl": float('inf'),
                "mean": float(mean),
                "std_dev": 0.0
            }

        # Calculate capability indices
        cp = (upper_spec - lower_spec) / (6 * std_dev)
        cpu = (upper_spec - mean) / (3 * std_dev)
        cpl = (mean - lower_spec) / (3 * std_dev)
        cpk = min(cpu, cpl)

        return {
            "cpk": float(cpk),
            "cp": float(cp),
            "cpu": float(cpu),
            "cpl": float(cpl),
            "mean": float(mean),
            "std_dev": float(std_dev)
        }
