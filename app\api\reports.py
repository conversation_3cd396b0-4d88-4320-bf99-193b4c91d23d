"""
Reports API endpoints for report generation and management
"""

from typing import List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_
import structlog

from app.database import get_db
from app.models.user import User
from app.models.report import Report, ReportTemplate, ReportStatus, ReportType, ReportFormat
from app.schemas.report import (
    ReportCreate,
    ReportUpdate,
    Report as ReportSchema,
    ReportTemplateCreate,
    ReportTemplate as ReportTemplateSchema
)
from app.core.auth import get_current_verified_user, get_current_editor_user, get_current_admin_user
from app.services.report_generator import ReportGenerator
from app.tasks.report_generation import generate_report_task
from app.config import settings

router = APIRouter()
logger = structlog.get_logger()


@router.get("/", response_model=List[ReportSchema])
async def get_reports(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    report_type: Optional[ReportType] = Query(None),
    status: Optional[ReportStatus] = Query(None),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    current_user: User = Depends(get_current_verified_user),
    db: Session = Depends(get_db)
):
    """
    Get reports with filtering and pagination
    """
    query = db.query(Report)

    # Non-admin users can only see their own reports
    if not current_user.is_admin:
        query = query.filter(Report.generated_by == current_user.id)

    # Apply filters
    if report_type:
        query = query.filter(Report.report_type == report_type)

    if status:
        query = query.filter(Report.status == status)

    if start_date:
        query = query.filter(Report.period_start >= start_date)

    if end_date:
        query = query.filter(Report.period_end <= end_date)

    # Order by creation time (newest first)
    query = query.order_by(desc(Report.created_at))

    # Apply pagination
    reports = query.offset(skip).limit(limit).all()

    logger.info(
        "Retrieved reports",
        user_id=current_user.id,
        count=len(reports),
        filters={
            "report_type": report_type,
            "status": status,
            "start_date": start_date,
            "end_date": end_date
        }
    )

    return reports


@router.get("/{report_id}", response_model=ReportSchema)
async def get_report_by_id(
    report_id: int,
    current_user: User = Depends(get_current_verified_user),
    db: Session = Depends(get_db)
):
    """
    Get specific report by ID
    """
    report = db.query(Report).filter(Report.id == report_id).first()

    if not report:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Report not found"
        )

    # Check permissions
    if not current_user.is_admin and report.generated_by != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this report"
        )

    logger.info(
        "Retrieved report by ID",
        user_id=current_user.id,
        report_id=report_id,
        report_name=report.report_name
    )

    return report


@router.post("/", response_model=ReportSchema)
async def create_report(
    report_data: ReportCreate,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_editor_user),
    db: Session = Depends(get_db)
):
    """
    Create and generate new report
    """
    # Validate template if specified
    if report_data.template_id:
        template = db.query(ReportTemplate).filter(ReportTemplate.id == report_data.template_id).first()
        if not template:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Report template not found"
            )
        if not template.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Report template is not active"
            )

    # Create report record
    db_report = Report(
        **report_data.dict(),
        generated_by=current_user.id,
        status=ReportStatus.PENDING
    )

    db.add(db_report)
    db.commit()
    db.refresh(db_report)

    # Queue report generation task
    background_tasks.add_task(
        generate_report_task.delay,
        report_id=db_report.id
    )

    logger.info(
        "Created report",
        user_id=current_user.id,
        report_id=db_report.id,
        report_name=db_report.report_name,
        report_type=db_report.report_type
    )

    return db_report


@router.put("/{report_id}", response_model=ReportSchema)
async def update_report(
    report_id: int,
    report_update: ReportUpdate,
    current_user: User = Depends(get_current_editor_user),
    db: Session = Depends(get_db)
):
    """
    Update existing report
    """
    db_report = db.query(Report).filter(Report.id == report_id).first()

    if not db_report:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Report not found"
        )

    # Check permissions
    if not current_user.is_admin and db_report.generated_by != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to update this report"
        )

    # Update fields
    update_data = report_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_report, field, value)

    db.commit()
    db.refresh(db_report)

    logger.info(
        "Updated report",
        user_id=current_user.id,
        report_id=report_id,
        updated_fields=list(update_data.keys())
    )

    return db_report


@router.delete("/{report_id}")
async def delete_report(
    report_id: int,
    current_user: User = Depends(get_current_editor_user),
    db: Session = Depends(get_db)
):
    """
    Delete report
    """
    db_report = db.query(Report).filter(Report.id == report_id).first()

    if not db_report:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Report not found"
        )

    # Check permissions
    if not current_user.is_admin and db_report.generated_by != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to delete this report"
        )

    # Delete associated file if exists
    if db_report.file_path:
        try:
            import os
            if os.path.exists(db_report.file_path):
                os.remove(db_report.file_path)
        except Exception as e:
            logger.warning(
                "Failed to delete report file",
                report_id=report_id,
                file_path=db_report.file_path,
                error=str(e)
            )

    db.delete(db_report)
    db.commit()

    logger.info(
        "Deleted report",
        user_id=current_user.id,
        report_id=report_id,
        report_name=db_report.report_name
    )

    return {"message": "Report deleted successfully"}


@router.get("/{report_id}/download")
async def download_report(
    report_id: int,
    current_user: User = Depends(get_current_verified_user),
    db: Session = Depends(get_db)
):
    """
    Download report file
    """
    report = db.query(Report).filter(Report.id == report_id).first()

    if not report:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Report not found"
        )

    # Check permissions
    if not current_user.is_admin and report.generated_by != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to download this report"
        )

    if not report.file_path or not report.is_completed:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Report file not available"
        )

    import os
    if not os.path.exists(report.file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Report file not found on disk"
        )

    logger.info(
        "Downloaded report",
        user_id=current_user.id,
        report_id=report_id,
        report_name=report.report_name
    )

    # Determine media type based on format
    media_type_map = {
        ReportFormat.PDF: "application/pdf",
        ReportFormat.DOCX: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        ReportFormat.XLSX: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        ReportFormat.HTML: "text/html",
        ReportFormat.JSON: "application/json"
    }

    media_type = media_type_map.get(report.report_format, "application/octet-stream")
    filename = f"{report.report_name}.{report.report_format.value}"

    return FileResponse(
        path=report.file_path,
        media_type=media_type,
        filename=filename
    )


@router.post("/{report_id}/regenerate", response_model=ReportSchema)
async def regenerate_report(
    report_id: int,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_editor_user),
    db: Session = Depends(get_db)
):
    """
    Regenerate existing report
    """
    db_report = db.query(Report).filter(Report.id == report_id).first()

    if not db_report:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Report not found"
        )

    # Check permissions
    if not current_user.is_admin and db_report.generated_by != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to regenerate this report"
        )

    # Reset report status
    db_report.status = ReportStatus.PENDING
    db_report.generated_at = None
    db_report.error_message = None
    db_report.retry_count = 0

    db.commit()

    # Queue report generation task
    background_tasks.add_task(
        generate_report_task.delay,
        report_id=db_report.id
    )

    logger.info(
        "Regenerating report",
        user_id=current_user.id,
        report_id=report_id,
        report_name=db_report.report_name
    )

    return db_report


# Report Template endpoints
@router.get("/templates/", response_model=List[ReportTemplateSchema])
async def get_report_templates(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    report_type: Optional[ReportType] = Query(None),
    is_active: Optional[bool] = Query(None),
    current_user: User = Depends(get_current_verified_user),
    db: Session = Depends(get_db)
):
    """
    Get report templates
    """
    query = db.query(ReportTemplate)

    # Apply filters
    if report_type:
        query = query.filter(ReportTemplate.report_type == report_type)

    if is_active is not None:
        query = query.filter(ReportTemplate.is_active == is_active)

    # Non-admin users can only see public templates or their own
    if not current_user.is_admin:
        query = query.filter(
            or_(
                ReportTemplate.is_public == True,
                ReportTemplate.created_by == current_user.id
            )
        )

    # Order by name
    query = query.order_by(ReportTemplate.template_name)

    # Apply pagination
    templates = query.offset(skip).limit(limit).all()

    logger.info(
        "Retrieved report templates",
        user_id=current_user.id,
        count=len(templates)
    )

    return templates


@router.get("/templates/{template_id}", response_model=ReportTemplateSchema)
async def get_report_template_by_id(
    template_id: int,
    current_user: User = Depends(get_current_verified_user),
    db: Session = Depends(get_db)
):
    """
    Get specific report template by ID
    """
    template = db.query(ReportTemplate).filter(ReportTemplate.id == template_id).first()

    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Report template not found"
        )

    # Check permissions
    if not current_user.is_admin and not template.is_public and template.created_by != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this template"
        )

    return template


@router.post("/templates/", response_model=ReportTemplateSchema)
async def create_report_template(
    template_data: ReportTemplateCreate,
    current_user: User = Depends(get_current_editor_user),
    db: Session = Depends(get_db)
):
    """
    Create new report template
    """
    # Check if template name already exists
    existing = db.query(ReportTemplate).filter(ReportTemplate.template_name == template_data.template_name).first()
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Template name already exists"
        )

    # Create template
    db_template = ReportTemplate(
        **template_data.dict(),
        created_by=current_user.id
    )

    db.add(db_template)
    db.commit()
    db.refresh(db_template)

    logger.info(
        "Created report template",
        user_id=current_user.id,
        template_id=db_template.id,
        template_name=db_template.template_name
    )

    return db_template


@router.put("/templates/{template_id}", response_model=ReportTemplateSchema)
async def update_report_template(
    template_id: int,
    template_update: ReportTemplateCreate,
    current_user: User = Depends(get_current_editor_user),
    db: Session = Depends(get_db)
):
    """
    Update existing report template
    """
    db_template = db.query(ReportTemplate).filter(ReportTemplate.id == template_id).first()

    if not db_template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Report template not found"
        )

    # Check permissions
    if not current_user.is_admin and db_template.created_by != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to update this template"
        )

    # Update fields
    update_data = template_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_template, field, value)

    db.commit()
    db.refresh(db_template)

    logger.info(
        "Updated report template",
        user_id=current_user.id,
        template_id=template_id,
        updated_fields=list(update_data.keys())
    )

    return db_template


@router.delete("/templates/{template_id}")
async def delete_report_template(
    template_id: int,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Delete report template (admin only)
    """
    db_template = db.query(ReportTemplate).filter(ReportTemplate.id == template_id).first()

    if not db_template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Report template not found"
        )

    # Check if template is being used by any reports
    reports_using_template = db.query(Report).filter(Report.template_id == template_id).count()
    if reports_using_template > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Cannot delete template. It is being used by {reports_using_template} reports."
        )

    db.delete(db_template)
    db.commit()

    logger.info(
        "Deleted report template",
        user_id=current_user.id,
        template_id=template_id,
        template_name=db_template.template_name
    )

    return {"message": "Report template deleted successfully"}
