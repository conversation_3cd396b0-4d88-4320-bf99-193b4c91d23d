"""
User model for authentication and authorization
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum

from app.database import Base


class UserRole(str, enum.Enum):
    """User roles enumeration"""
    ADMIN = "admin"
    ENGINEER = "engineer"
    OPERATOR = "operator"
    VIEWER = "viewer"


class User(Base):
    """User model for authentication and authorization"""

    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    full_name = Column(String(100), nullable=False)
    hashed_password = Column(String(255), nullable=False)
    role = Column(Enum(UserRole), default=UserRole.VIEWER, nullable=False)
    is_active = Column(<PERSON>olean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_login = Column(DateTime(timezone=True), nullable=True)

    # Employee information
    employee_id = Column(String(20), unique=True, nullable=True)
    department = Column(String(50), nullable=True)
    site_id = Column(Integer, nullable=True)  # Will be foreign key to sites table

    def __repr__(self):
        return f"<User(username='{self.username}', email='{self.email}', role='{self.role}')>"

    @property
    def is_admin(self) -> bool:
        """Check if user is admin"""
        return self.role == UserRole.ADMIN

    @property
    def is_engineer(self) -> bool:
        """Check if user is engineer or admin"""
        return self.role in [UserRole.ADMIN, UserRole.ENGINEER]

    @property
    def can_edit(self) -> bool:
        """Check if user can edit data"""
        return self.role in [UserRole.ADMIN, UserRole.ENGINEER, UserRole.OPERATOR]

    @property
    def can_view(self) -> bool:
        """Check if user can view data"""
        return self.is_active and self.is_verified
