"""
Security utilities for password hashing and JWT tokens
"""

from datetime import datetime, timedelta
from typing import Optional, Union
from jose import JW<PERSON>rror, jwt
from passlib.context import CryptContext
import secrets

from app.config import settings

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    Create JWT access token
    """
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode.update({"exp": expire, "type": "access"})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


def create_refresh_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    Create JWT refresh token
    """
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)

    to_encode.update({"exp": expire, "type": "refresh"})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


def verify_token(token: str, token_type: str = "access") -> Optional[dict]:
    """
    Verify JWT token and return payload
    """
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        if payload.get("type") != token_type:
            return None
        return payload
    except JWTError:
        return None


def get_password_hash(password: str) -> str:
    """
    Hash password using bcrypt
    """
    return pwd_context.hash(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify password against hash
    """
    return pwd_context.verify(plain_password, hashed_password)


def generate_password_reset_token() -> str:
    """
    Generate secure password reset token
    """
    return secrets.token_urlsafe(32)


def generate_verification_token() -> str:
    """
    Generate secure email verification token
    """
    return secrets.token_urlsafe(32)


def create_password_reset_token(email: str) -> str:
    """
    Create JWT token for password reset
    """
    delta = timedelta(hours=1)  # Password reset tokens expire in 1 hour
    to_encode = {"email": email, "type": "password_reset"}
    return create_access_token(to_encode, expires_delta=delta)


def verify_password_reset_token(token: str) -> Optional[str]:
    """
    Verify password reset token and return email
    """
    payload = verify_token(token, token_type="password_reset")
    if payload:
        return payload.get("email")
    return None


def create_email_verification_token(email: str) -> str:
    """
    Create JWT token for email verification
    """
    delta = timedelta(days=7)  # Email verification tokens expire in 7 days
    to_encode = {"email": email, "type": "email_verification"}
    return create_access_token(to_encode, expires_delta=delta)


def verify_email_verification_token(token: str) -> Optional[str]:
    """
    Verify email verification token and return email
    """
    payload = verify_token(token, token_type="email_verification")
    if payload:
        return payload.get("email")
    return None
