﻿# YieldDoc - Automated Yield Reporting System

## 🚀 Product Name Suggestions

1. **YieldDoc** - *yielddoc.com* (Available)
2. **SemiYield Pro** - *semiyieldpro.com* (Available)
3. **WaferInsight** - *waferinsight.com* (Available)
4. **ChipAnalytics** - *chipanalytics.com* (Available)
5. **FabReporter** - *fabreporter.com* (Available)

## 📋 Overview

YieldDoc is a comprehensive automated yield reporting system designed specifically for semiconductor manufacturing. It eliminates manual, time-consuming yield report generation by automating data processing, analysis, and report creation across multiple fabrication sites.

## 🏗️ System Architecture

```mermaid
graph TB
    subgraph "Data Sources"
        A[STDF Files]
        B[CSV Data]
        C[MES Database]
        D[Test Equipment]
    end

    subgraph "Data Ingestion Layer"
        E[Data Collectors]
        F[File Processors]
        G[API Connectors]
    end

    subgraph "Core Processing"
        H[Data Validation]
        I[Statistical Engine]
        J[Yield Calculator]
        K[Trend Analyzer]
    end

    subgraph "Storage Layer"
        L[(PostgreSQL)]
        M[(Redis Cache)]
    end

    subgraph "Application Layer"
        N[FastAPI Backend]
        O[Authentication Service]
        P[Report Generator]
        Q[Task Scheduler]
    end

    subgraph "Presentation Layer"
        R[Plotly Dashboards]
        S[Web Interface]
        T[Email Reports]
        U[API Endpoints]
    end

    A --> E
    B --> F
    C --> G
    D --> G

    E --> H
    F --> H
    G --> H

    H --> I
    I --> J
    J --> K

    I --> L
    J --> L
    K --> L
    L --> M

    L --> N
    M --> N
    N --> O
    N --> P
    N --> Q

    N --> R
    N --> S
    P --> T
    N --> U
```

## 🔄 Data Processing Workflow

```mermaid
flowchart TD
    A[Raw Data Input] --> B{Data Type?}
    B -->|STDF| C[STDF Parser]
    B -->|CSV| D[CSV Processor]
    B -->|Database| E[DB Connector]

    C --> F[Data Validation]
    D --> F
    E --> F

    F --> G{Valid Data?}
    G -->|No| H[Error Logging]
    G -->|Yes| I[Statistical Processing]

    I --> J[Yield Calculation]
    J --> K[Trend Analysis]
    K --> L[Anomaly Detection]

    L --> M[Data Storage]
    M --> N[Report Generation]
    N --> O[Dashboard Update]
    O --> P[Notification Service]

    H --> Q[Alert System]
    P --> R[Email Delivery]
    Q --> R
```

## 📁 Project Structure

```
yielddoc/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI application entry point
│   ├── config.py              # Configuration settings
│   ├── database.py            # Database connection and setup
│   ├── models/                # SQLAlchemy models
│   │   ├── __init__.py
│   │   ├── user.py
│   │   ├── yield_data.py
│   │   ├── report.py
│   │   └── site.py
│   ├── schemas/               # Pydantic schemas
│   │   ├── __init__.py
│   │   ├── user.py
│   │   ├── yield_data.py
│   │   └── report.py
│   ├── api/                   # API routes
│   │   ├── __init__.py
│   │   ├── auth.py
│   │   ├── yield_data.py
│   │   ├── reports.py
│   │   └── dashboard.py
│   ├── core/                  # Core business logic
│   │   ├── __init__.py
│   │   ├── auth.py
│   │   ├── security.py
│   │   └── permissions.py
│   ├── services/              # Business services
│   │   ├── __init__.py
│   │   ├── data_processor.py
│   │   ├── report_generator.py
│   │   ├── statistical_analyzer.py
│   │   ├── email_service.py
│   │   └── dashboard_service.py
│   ├── utils/                 # Utility functions
│   │   ├── __init__.py
│   │   ├── stdf_parser.py
│   │   ├── csv_processor.py
│   │   ├── wafer_mapper.py
│   │   └── helpers.py
│   └── tasks/                 # Celery tasks
│       ├── __init__.py
│       ├── data_processing.py
│       ├── report_generation.py
│       └── email_tasks.py
├── dashboard/                 # Plotly Dash application
│   ├── __init__.py
│   ├── app.py
│   ├── layouts/
│   │   ├── __init__.py
│   │   ├── main_dashboard.py
│   │   ├── yield_trends.py
│   │   └── wafer_maps.py
│   └── components/
│       ├── __init__.py
│       ├── charts.py
│       ├── tables.py
│       └── filters.py
├── templates/                 # Report templates
│   ├── daily_report.docx
│   ├── weekly_report.docx
│   └── monthly_report.docx
├── tests/                     # Test files
│   ├── __init__.py
│   ├── test_api.py
│   ├── test_services.py
│   └── test_utils.py
├── scripts/                   # Utility scripts
│   ├── init_db.py
│   ├── sample_data.py
│   └── backup_db.py
├── docker/                    # Docker configuration
│   ├── Dockerfile
│   ├── docker-compose.yml
│   └── nginx.conf
├── docs/                      # Documentation
│   ├── api.md
│   ├── deployment.md
│   └── user_guide.md
├── requirements.txt           # Python dependencies
├── .env.example              # Environment variables template
├── .gitignore                # Git ignore file
├── alembic.ini               # Database migration config
└── celery_config.py          # Celery configuration
```

## 🚀 Quick Start

### Prerequisites

- Python 3.9+
- PostgreSQL 12+
- Redis 6+
- Docker (optional)

### Installation

1. **Clone the repository:**
```bash
git clone https://github.com/HectorTa1989/yielddoc.git
cd yielddoc
```

2. **Create virtual environment:**
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install dependencies:**
```bash
pip install -r requirements.txt
```

4. **Setup environment variables:**
```bash
cp .env.example .env
# Edit .env with your configuration
```

5. **Initialize database:**
```bash
python scripts/init_db.py
```

6. **Start the application:**
```bash
uvicorn app.main:app --reload
```

7. **Start Celery worker:**
```bash
celery -A app.tasks worker --loglevel=info
```

8. **Start dashboard:**
```bash
python dashboard/app.py
```

## 🐳 Docker Deployment

```bash
docker-compose up -d
```

## 📊 Key Features

- **Automated Data Processing**: STDF, CSV, and database ingestion
- **Real-time Dashboards**: Interactive wafer maps and KPI monitoring
- **Scheduled Reporting**: Daily, weekly, and monthly automated reports
- **Statistical Analysis**: Control limits, Pareto analysis, trend detection
- **Multi-site Support**: Cross-site yield comparison and benchmarking
- **Role-based Access**: Secure authentication and authorization
- **Email Integration**: Automated report delivery
- **RESTful API**: MES system integration ready

## 🔧 Configuration

Key configuration options in `.env`:

```env
DATABASE_URL=postgresql://user:password@localhost/yielddoc
REDIS_URL=redis://localhost:6379
SECRET_KEY=your-secret-key
SMTP_SERVER=smtp.company.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=password
```

## 📈 API Documentation

Once running, visit:
- API Documentation: `http://localhost:8000/docs`
- Dashboard: `http://localhost:8050`

## 🧪 Testing

```bash
pytest tests/
```

## 📝 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Open Pull Request

## 📞 Support

For support, email <EMAIL> or create an issue on GitHub.
