version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: yielddoc_postgres
    environment:
      POSTGRES_DB: yielddoc
      POSTGRES_USER: yielddoc_user
      POSTGRES_PASSWORD: yielddoc_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U yielddoc_user -d yielddoc"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: yielddoc_redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # YieldDoc API
  api:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: yielddoc_api
    environment:
      - DATABASE_URL=**********************************************************/yielddoc
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
      - SECRET_KEY=your-super-secret-key-change-this-in-production
      - DEBUG=False
    volumes:
      - ../uploads:/app/uploads
      - ../reports:/app/reports
      - ../temp:/app/temp
      - ../logs:/app/logs
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Celery Worker
  celery_worker:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: yielddoc_celery_worker
    command: celery -A app.tasks worker --loglevel=info --concurrency=4
    environment:
      - DATABASE_URL=**********************************************************/yielddoc
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
      - SECRET_KEY=your-super-secret-key-change-this-in-production
    volumes:
      - ../uploads:/app/uploads
      - ../reports:/app/reports
      - ../temp:/app/temp
      - ../logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

  # Celery Beat (Scheduler)
  celery_beat:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: yielddoc_celery_beat
    command: celery -A app.tasks beat --loglevel=info
    environment:
      - DATABASE_URL=**********************************************************/yielddoc
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
      - SECRET_KEY=your-super-secret-key-change-this-in-production
    volumes:
      - ../uploads:/app/uploads
      - ../reports:/app/reports
      - ../temp:/app/temp
      - ../logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

  # Flower (Celery Monitoring)
  flower:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: yielddoc_flower
    command: celery -A app.tasks flower --port=5555
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
    ports:
      - "5555:5555"
    depends_on:
      - redis
    restart: unless-stopped

  # Dash Dashboard
  dashboard:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: yielddoc_dashboard
    command: python dashboard/app.py
    environment:
      - DATABASE_URL=**********************************************************/yielddoc
      - REDIS_URL=redis://redis:6379/0
      - DASH_HOST=0.0.0.0
      - DASH_PORT=8050
    ports:
      - "8050:8050"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: yielddoc_nginx
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - api
      - dashboard
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
