"""
Data processing service for STDF and CSV file handling
"""

import os
import csv
import tempfile
from typing import Dict, List, Any, Optional
from datetime import datetime
from fastapi import UploadFile
from sqlalchemy.orm import Session
import pandas as pd
import structlog

from app.models.yield_data import Yi<PERSON>Data, TestResult, WaferData, DataSource, TestStatus
from app.models.site import Site
from app.utils.stdf_parser import STDFParser
from app.utils.csv_processor import CSVProcessor
from app.config import settings

logger = structlog.get_logger()


class DataProcessor:
    """Service for processing uploaded data files"""

    def __init__(self, db: Session):
        self.db = db
        self.stdf_parser = STDFParser()
        self.csv_processor = CSVProcessor()

    async def process_stdf_file(
        self,
        file: UploadFile,
        site_id: int,
        user_id: int
    ) -> Dict[str, Any]:
        """
        Process uploaded STDF file
        """
        logger.info(
            "Starting STDF file processing",
            filename=file.filename,
            site_id=site_id,
            user_id=user_id
        )

        # Save uploaded file temporarily
        temp_file_path = None
        try:
            # Create temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix='.stdf') as temp_file:
                temp_file_path = temp_file.name
                content = await file.read()
                temp_file.write(content)

            # Parse STDF file
            parsed_data = self.stdf_parser.parse_file(temp_file_path)

            # Process parsed data
            result = await self._process_parsed_stdf_data(
                parsed_data,
                site_id,
                user_id,
                file.filename
            )

            logger.info(
                "STDF file processing completed",
                filename=file.filename,
                lots_processed=result.get('lots_processed', 0)
            )

            return result

        except Exception as e:
            logger.error(
                "STDF file processing failed",
                filename=file.filename,
                error=str(e)
            )
            raise

        finally:
            # Clean up temporary file
            if temp_file_path and os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    async def process_csv_file(
        self,
        file: UploadFile,
        site_id: int,
        user_id: int
    ) -> Dict[str, Any]:
        """
        Process uploaded CSV file
        """
        logger.info(
            "Starting CSV file processing",
            filename=file.filename,
            site_id=site_id,
            user_id=user_id
        )

        try:
            # Read CSV content
            content = await file.read()
            csv_data = content.decode('utf-8')

            # Parse CSV data
            parsed_data = self.csv_processor.parse_csv_data(csv_data)

            # Process parsed data
            result = await self._process_parsed_csv_data(
                parsed_data,
                site_id,
                user_id,
                file.filename
            )

            logger.info(
                "CSV file processing completed",
                filename=file.filename,
                records_processed=result.get('records_processed', 0)
            )

            return result

        except Exception as e:
            logger.error(
                "CSV file processing failed",
                filename=file.filename,
                error=str(e)
            )
            raise

    async def _process_parsed_stdf_data(
        self,
        parsed_data: Dict[str, Any],
        site_id: int,
        user_id: int,
        filename: str
    ) -> Dict[str, Any]:
        """
        Process parsed STDF data and create database records
        """
        yield_data_ids = []
        lots_processed = 0

        # Group data by lot
        lots_data = self._group_stdf_data_by_lot(parsed_data)

        for lot_id, lot_data in lots_data.items():
            try:
                # Create yield data record
                yield_data = await self._create_yield_data_from_stdf(
                    lot_data,
                    site_id,
                    user_id,
                    filename
                )

                if yield_data:
                    yield_data_ids.append(yield_data.id)
                    lots_processed += 1

                    # Create test results
                    await self._create_test_results_from_stdf(yield_data.id, lot_data)

                    # Create wafer data if available
                    await self._create_wafer_data_from_stdf(yield_data.id, lot_data)

            except Exception as e:
                logger.error(
                    "Failed to process lot from STDF",
                    lot_id=lot_id,
                    error=str(e)
                )
                continue

        return {
            'lots_processed': lots_processed,
            'yield_data_ids': yield_data_ids
        }

    async def _process_parsed_csv_data(
        self,
        parsed_data: List[Dict[str, Any]],
        site_id: int,
        user_id: int,
        filename: str
    ) -> Dict[str, Any]:
        """
        Process parsed CSV data and create database records
        """
        yield_data_ids = []
        records_processed = 0

        # Group data by lot if multiple records per lot
        lots_data = self._group_csv_data_by_lot(parsed_data)

        for lot_id, lot_records in lots_data.items():
            try:
                # Create yield data record
                yield_data = await self._create_yield_data_from_csv(
                    lot_records,
                    site_id,
                    user_id,
                    filename
                )

                if yield_data:
                    yield_data_ids.append(yield_data.id)
                    records_processed += len(lot_records)

            except Exception as e:
                logger.error(
                    "Failed to process lot from CSV",
                    lot_id=lot_id,
                    error=str(e)
                )
                continue

        return {
            'records_processed': records_processed,
            'yield_data_ids': yield_data_ids
        }
