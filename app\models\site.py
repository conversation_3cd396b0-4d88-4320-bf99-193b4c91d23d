"""
Site model for manufacturing sites
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Float
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from app.database import Base


class Site(Base):
    """Manufacturing site model"""

    __tablename__ = "sites"

    id = Column(Integer, primary_key=True, index=True)
    site_code = Column(String(10), unique=True, index=True, nullable=False)
    site_name = Column(String(100), nullable=False)
    location = Column(String(100), nullable=False)
    country = Column(String(50), nullable=False)

    # Contact information
    contact_email = Column(String(100), nullable=True)
    contact_phone = Column(String(20), nullable=True)
    manager_name = Column(String(100), nullable=True)

    # Site configuration
    timezone = Column(String(50), default="UTC", nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)

    # Capacity information
    max_wafers_per_day = Column(Integer, nullable=True)
    number_of_lines = Column(Integer, nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Additional information
    description = Column(Text, nullable=True)
    notes = Column(Text, nullable=True)

    def __repr__(self):
        return f"<Site(code='{self.site_code}', name='{self.site_name}', location='{self.location}')>"

    @property
    def display_name(self) -> str:
        """Get display name for site"""
        return f"{self.site_code} - {self.site_name}"
