"""
STDF (Standard Test Data Format) file parser
"""

import struct
from typing import Dict, List, Any, Optional
from datetime import datetime
import structlog

logger = structlog.get_logger()


class STDFParser:
    """Parser for STDF files"""

    def __init__(self):
        self.record_types = {
            0: "FAR",   # File Attributes Record
            1: "ATR",   # Audit Trail Record
            2: "MIR",   # Master Information Record
            5: "SDR",   # Site Description Record
            10: "WIR",  # Wafer Information Record
            15: "PIR",  # Part Information Record
            20: "PTR",  # Parametric Test Record
            25: "FTR",  # Functional Test Record
            30: "WRR",  # Wafer Results Record
            50: "MRR",  # Master Results Record
        }

    def parse_file(self, file_path: str) -> Dict[str, Any]:
        """
        Parse STDF file and extract test data
        """
        logger.info("Starting STDF file parsing", file_path=file_path)

        try:
            with open(file_path, 'rb') as file:
                parsed_data = {
                    "file_info": {},
                    "lots": {},
                    "wafers": {},
                    "test_results": [],
                    "summary": {}
                }

                while True:
                    # Read record header
                    header = file.read(4)
                    if len(header) < 4:
                        break

                    # Parse header
                    record_length = struct.unpack('<H', header[:2])[0]
                    record_type = header[2]
                    record_subtype = header[3]

                    # Read record data
                    if record_length > 0:
                        record_data = file.read(record_length)
                        if len(record_data) < record_length:
                            break

                        # Process record based on type
                        self._process_record(
                            record_type,
                            record_subtype,
                            record_data,
                            parsed_data
                        )

                # Post-process data
                self._post_process_data(parsed_data)

                logger.info(
                    "STDF file parsing completed",
                    lots_found=len(parsed_data["lots"]),
                    wafers_found=len(parsed_data["wafers"]),
                    test_results=len(parsed_data["test_results"])
                )

                return parsed_data

        except Exception as e:
            logger.error("STDF file parsing failed", error=str(e))
            raise

    def _process_record(
        self,
        record_type: int,
        record_subtype: int,
        data: bytes,
        parsed_data: Dict[str, Any]
    ):
        """
        Process individual STDF record
        """
        try:
            if record_type == 0 and record_subtype == 10:  # FAR
                self._parse_far_record(data, parsed_data)
            elif record_type == 1 and record_subtype == 20:  # MIR
                self._parse_mir_record(data, parsed_data)
            elif record_type == 2 and record_subtype == 10:  # WIR
                self._parse_wir_record(data, parsed_data)
            elif record_type == 15 and record_subtype == 10:  # PIR
                self._parse_pir_record(data, parsed_data)
            elif record_type == 15 and record_subtype == 20:  # PTR
                self._parse_ptr_record(data, parsed_data)
            elif record_type == 15 and record_subtype == 15:  # FTR
                self._parse_ftr_record(data, parsed_data)
            elif record_type == 2 and record_subtype == 20:  # WRR
                self._parse_wrr_record(data, parsed_data)
            elif record_type == 1 and record_subtype == 50:  # MRR
                self._parse_mrr_record(data, parsed_data)

        except Exception as e:
            logger.warning(
                "Failed to parse STDF record",
                record_type=record_type,
                record_subtype=record_subtype,
                error=str(e)
            )

    def _parse_far_record(self, data: bytes, parsed_data: Dict[str, Any]):
        """Parse File Attributes Record"""
        if len(data) >= 2:
            cpu_type = data[0]
            stdf_version = data[1]
            parsed_data["file_info"]["cpu_type"] = cpu_type
            parsed_data["file_info"]["stdf_version"] = stdf_version

    def _parse_mir_record(self, data: bytes, parsed_data: Dict[str, Any]):
        """Parse Master Information Record"""
        if len(data) >= 8:
            # Extract basic information
            setup_time = struct.unpack('<I', data[:4])[0]
            start_time = struct.unpack('<I', data[4:8])[0]

            parsed_data["file_info"]["setup_time"] = datetime.fromtimestamp(setup_time)
            parsed_data["file_info"]["start_time"] = datetime.fromtimestamp(start_time)

            # Extract additional fields if available
            offset = 8
            if len(data) > offset:
                # Parse lot ID, part type, etc. (simplified)
                try:
                    lot_id_len = data[offset] if offset < len(data) else 0
                    if lot_id_len > 0 and offset + 1 + lot_id_len <= len(data):
                        lot_id = data[offset + 1:offset + 1 + lot_id_len].decode('ascii', errors='ignore')
                        parsed_data["file_info"]["lot_id"] = lot_id
                except:
                    pass

    def _parse_wir_record(self, data: bytes, parsed_data: Dict[str, Any]):
        """Parse Wafer Information Record"""
        if len(data) >= 9:
            head_num = data[0]
            site_grp = data[1]
            start_time = struct.unpack('<I', data[2:6])[0]
            wafer_id_len = data[6]

            wafer_id = ""
            if wafer_id_len > 0 and len(data) > 7 + wafer_id_len:
                wafer_id = data[7:7 + wafer_id_len].decode('ascii', errors='ignore')

            wafer_key = f"{head_num}_{site_grp}_{wafer_id}"
            parsed_data["wafers"][wafer_key] = {
                "head_num": head_num,
                "site_grp": site_grp,
                "wafer_id": wafer_id,
                "start_time": datetime.fromtimestamp(start_time),
                "die_count": 0,
                "good_die": 0
            }

    def _parse_pir_record(self, data: bytes, parsed_data: Dict[str, Any]):
        """Parse Part Information Record"""
        if len(data) >= 2:
            head_num = data[0]
            site_num = data[1]
            # Additional part information can be extracted here

    def _parse_ptr_record(self, data: bytes, parsed_data: Dict[str, Any]):
        """Parse Parametric Test Record"""
        if len(data) >= 15:
            test_num = struct.unpack('<I', data[0:4])[0]
            head_num = data[4]
            site_num = data[5]
            test_flags = data[6]
            parm_flags = data[7]
            result = struct.unpack('<f', data[8:12])[0]

            # Extract test name if available
            test_name = f"Test_{test_num}"
            offset = 12
            if len(data) > offset:
                try:
                    test_name_len = data[offset]
                    if test_name_len > 0 and offset + 1 + test_name_len <= len(data):
                        test_name = data[offset + 1:offset + 1 + test_name_len].decode('ascii', errors='ignore')
                except:
                    pass

            test_result = {
                "test_num": test_num,
                "test_name": test_name,
                "head_num": head_num,
                "site_num": site_num,
                "result": result,
                "test_flags": test_flags,
                "status": "pass" if (test_flags & 0x01) == 0 else "fail"
            }

            parsed_data["test_results"].append(test_result)

    def _parse_ftr_record(self, data: bytes, parsed_data: Dict[str, Any]):
        """Parse Functional Test Record"""
        if len(data) >= 22:
            test_num = struct.unpack('<I', data[0:4])[0]
            head_num = data[4]
            site_num = data[5]
            test_flags = data[6]

            test_result = {
                "test_num": test_num,
                "test_name": f"Func_Test_{test_num}",
                "head_num": head_num,
                "site_num": site_num,
                "test_flags": test_flags,
                "status": "pass" if (test_flags & 0x01) == 0 else "fail",
                "test_type": "functional"
            }

            parsed_data["test_results"].append(test_result)

    def _parse_wrr_record(self, data: bytes, parsed_data: Dict[str, Any]):
        """Parse Wafer Results Record"""
        if len(data) >= 9:
            head_num = data[0]
            site_grp = data[1]
            finish_time = struct.unpack('<I', data[2:6])[0]
            part_count = struct.unpack('<I', data[6:10])[0]

            wafer_key = f"{head_num}_{site_grp}"
            if wafer_key in parsed_data["wafers"]:
                parsed_data["wafers"][wafer_key]["finish_time"] = datetime.fromtimestamp(finish_time)
                parsed_data["wafers"][wafer_key]["part_count"] = part_count

    def _parse_mrr_record(self, data: bytes, parsed_data: Dict[str, Any]):
        """Parse Master Results Record"""
        if len(data) >= 4:
            finish_time = struct.unpack('<I', data[0:4])[0]
            parsed_data["file_info"]["finish_time"] = datetime.fromtimestamp(finish_time)

    def _post_process_data(self, parsed_data: Dict[str, Any]):
        """Post-process parsed data to calculate yields and organize by lots"""
        # Group test results by lot/wafer
        lot_data = {}

        for test_result in parsed_data["test_results"]:
            lot_id = parsed_data["file_info"].get("lot_id", "UNKNOWN_LOT")

            if lot_id not in lot_data:
                lot_data[lot_id] = {
                    "lot_id": lot_id,
                    "test_results": [],
                    "total_tests": 0,
                    "passed_tests": 0,
                    "failed_tests": 0
                }

            lot_data[lot_id]["test_results"].append(test_result)
            lot_data[lot_id]["total_tests"] += 1

            if test_result["status"] == "pass":
                lot_data[lot_id]["passed_tests"] += 1
            else:
                lot_data[lot_id]["failed_tests"] += 1

        # Calculate yield for each lot
        for lot_id, data in lot_data.items():
            if data["total_tests"] > 0:
                data["yield_percent"] = (data["passed_tests"] / data["total_tests"]) * 100
            else:
                data["yield_percent"] = 0.0

        parsed_data["lots"] = lot_data

        # Calculate summary statistics
        total_tests = sum(lot["total_tests"] for lot in lot_data.values())
        total_passed = sum(lot["passed_tests"] for lot in lot_data.values())

        parsed_data["summary"] = {
            "total_lots": len(lot_data),
            "total_tests": total_tests,
            "total_passed": total_passed,
            "total_failed": total_tests - total_passed,
            "overall_yield": (total_passed / total_tests * 100) if total_tests > 0 else 0.0
        }
